<script lang="ts">
	import MessageInput from '$lib/components/conversation/MessageInput.svelte';
	
	function handleSend(event: CustomEvent) {
		console.log('Message sent:', event.detail);
		// In a real application, you would handle the message sending here
	}
	
	function handleTyping(event: CustomEvent) {
		console.log('Typing status:', event.detail.isTyping);
	}
</script>

<svelte:head>
	<title>Enhanced Autocomplete Test - MessageInput</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 py-8">
	<div class="mx-auto max-w-4xl px-4">
		<div class="mb-8">
			<h1 class="text-3xl font-bold text-gray-900 mb-4">Enhanced MessageInput Autocomplete Test</h1>
			<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
				<h2 class="text-lg font-semibold text-blue-900 mb-2">🚀 Enhanced Keyboard Navigation Features:</h2>
				<ul class="text-blue-800 space-y-2 text-sm">
					<li><strong>🎯 Initial Focus Behavior:</strong> Press <kbd class="bg-blue-200 px-2 py-1 rounded text-xs">↑</kbd> when suggestions appear to focus on the last suggestion</li>
					<li><strong>🔄 Wrap-around Navigation:</strong> Use <kbd class="bg-blue-200 px-2 py-1 rounded text-xs">↑</kbd>/<kbd class="bg-blue-200 px-2 py-1 rounded text-xs">↓</kbd> to navigate with seamless wrap-around</li>
					<li><strong>✅ Selection:</strong> Press <kbd class="bg-blue-200 px-2 py-1 rounded text-xs">Enter</kbd> to insert template and return focus to textarea</li>
					<li><strong>🚪 Exit:</strong> Press <kbd class="bg-blue-200 px-2 py-1 rounded text-xs">Esc</kbd> or click outside to close and return focus</li>
					<li><strong>🎨 Visual Feedback:</strong> Enhanced highlighting with ring effects and smooth transitions</li>
				</ul>
			</div>
			
			<div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
				<h3 class="text-lg font-semibold text-green-900 mb-2">🧪 Test Keywords:</h3>
				<div class="grid grid-cols-2 md:grid-cols-5 gap-2 text-sm">
					<span class="bg-green-100 px-2 py-1 rounded text-green-800 font-mono">hello</span>
					<span class="bg-green-100 px-2 py-1 rounded text-green-800 font-mono">thank</span>
					<span class="bg-green-100 px-2 py-1 rounded text-green-800 font-mono">sorry</span>
					<span class="bg-green-100 px-2 py-1 rounded text-green-800 font-mono">help</span>
					<span class="bg-green-100 px-2 py-1 rounded text-green-800 font-mono">close</span>
					<span class="bg-green-100 px-2 py-1 rounded text-green-800 font-mono">bill</span>
					<span class="bg-green-100 px-2 py-1 rounded text-green-800 font-mono">tech</span>
					<span class="bg-green-100 px-2 py-1 rounded text-green-800 font-mono">refund</span>
					<span class="bg-green-100 px-2 py-1 rounded text-green-800 font-mono">escalate</span>
					<span class="bg-green-100 px-2 py-1 rounded text-green-800 font-mono">follow</span>
				</div>
			</div>
		</div>
		
		<div class="bg-white rounded-lg shadow-sm border border-gray-200">
			<div class="p-4 border-b border-gray-200">
				<h3 class="text-lg font-medium text-gray-900">Enhanced MessageInput Component</h3>
				<p class="text-sm text-gray-600 mt-1">Test the improved keyboard navigation - type keywords and use arrow keys!</p>
			</div>
			
			<!-- Simulated chat messages area -->
			<div class="p-4 min-h-[300px] bg-gray-50">
				<div class="text-center text-gray-500 text-sm space-y-2">
					<p>🎯 <strong>Keyboard Navigation Test Environment</strong></p>
					<p>Type any keyword above, then use keyboard navigation to select templates</p>
					<div class="mt-4 p-3 bg-white rounded border border-gray-200 text-left max-w-md mx-auto">
						<p class="text-xs text-gray-600 mb-2"><strong>Try this workflow:</strong></p>
						<ol class="text-xs text-gray-700 space-y-1">
							<li>1. Type "he" to see suggestions</li>
							<li>2. Press ↑ to focus last suggestion</li>
							<li>3. Use ↑/↓ to navigate</li>
							<li>4. Press Enter to select</li>
							<li>5. Notice focus returns to textarea</li>
						</ol>
					</div>
				</div>
			</div>
			
			<!-- MessageInput component -->
			<MessageInput 
				on:send={handleSend}
				on:typing={handleTyping}
			/>
		</div>
		
		<div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
			<h3 class="text-lg font-medium text-gray-900 mb-4">🎨 Visual Enhancements</h3>
			<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
				<div>
					<h4 class="font-medium text-gray-900 mb-2">Selected State Features:</h4>
					<ul class="text-sm text-gray-600 space-y-1">
						<li>• Blue gradient background</li>
						<li>• Ring effect with opacity</li>
						<li>• Smooth transitions (150ms)</li>
						<li>• Subtle elevation effect</li>
					</ul>
				</div>
				<div>
					<h4 class="font-medium text-gray-900 mb-2">Navigation Improvements:</h4>
					<ul class="text-sm text-gray-600 space-y-1">
						<li>• Wrap-around navigation</li>
						<li>• Initial focus on last item (↑)</li>
						<li>• Automatic scrolling to selection</li>
						<li>• Focus management on close</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</div>

<style>
	kbd {
		font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
		font-size: 0.75rem;
		font-weight: 600;
	}
</style>
