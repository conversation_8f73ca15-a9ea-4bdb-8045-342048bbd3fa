<script lang="ts">
	import { createEventDispatcher, onMount, onDestroy } from 'svelte';
	import { t, language } from '$lib/stores/i18n';
	const dispatch = createEventDispatcher();

	let messageText = '';
	let isTyping = false;
	let typingTimeout: number | null = null;
	let fileInput: HTMLInputElement;
	let selectedFiles: File[] = [];
	let showFilePreview = false;

	// Autocomplete/suggestion variables
	let showSuggestions = false;
	let suggestions: Array<{ id: string; keyword: string; template: string; description: string }> =
		[];
	let filteredSuggestions: Array<{
		id: string;
		keyword: string;
		template: string;
		description: string;
	}> = [];
	let selectedSuggestionIndex = -1;
	let textareaElement: HTMLTextAreaElement;
	let suggestionDropdown: HTMLElement;
	let currentTriggerWord = '';
	let triggerStartPosition = -1;

	// Pre-defined response templates
	const responseTemplates = [
		{
			id: 'greeting',
			keyword: 'hello',
			template: 'Hello! Thank you for contacting us. How can I assist you today?',
			description: 'Standard greeting message'
		},
		{
			id: 'thanks',
			keyword: 'thank',
			template:
				'Thank you for your message. We appreciate your feedback and will get back to you shortly.',
			description: 'Thank you response'
		},
		{
			id: 'apology',
			keyword: 'sorry',
			template:
				'We sincerely apologize for any inconvenience caused. We are working to resolve this issue as quickly as possible.',
			description: 'Apology message'
		},
		{
			id: 'followup',
			keyword: 'follow',
			template:
				'Thank you for your patience. We wanted to follow up on your previous inquiry. Is there anything else we can help you with?',
			description: 'Follow-up message'
		},
		{
			id: 'support',
			keyword: 'help',
			template:
				"I'm here to help! Please provide more details about your issue so I can assist you better.",
			description: 'Support assistance'
		},
		{
			id: 'closing',
			keyword: 'close',
			template:
				"Thank you for contacting us. If you have any other questions, please don't hesitate to reach out. Have a great day!",
			description: 'Closing message'
		},
		{
			id: 'escalate',
			keyword: 'escalate',
			template:
				'I understand your concern and will escalate this to our specialized team. You can expect a response within 24 hours.',
			description: 'Escalation message'
		},
		{
			id: 'refund',
			keyword: 'refund',
			template:
				"I understand you're inquiring about a refund. Let me check your account details and process this request for you.",
			description: 'Refund inquiry response'
		},
		{
			id: 'technical',
			keyword: 'tech',
			template:
				"I see you're experiencing a technical issue. Let me connect you with our technical support team who can better assist you.",
			description: 'Technical support'
		},
		{
			id: 'billing',
			keyword: 'bill',
			template:
				"For billing inquiries, I'll need to verify your account information. Please provide your account number or email address.",
			description: 'Billing inquiry'
		}
	];

	function handleSend() {
		if (messageText.trim() || selectedFiles.length > 0) {
			dispatch('send', {
				content: messageText.trim(),
				type: 'TEXT',
				files: selectedFiles
			});
			messageText = '';
			selectedFiles = [];
			showFilePreview = false;
			stopTyping();
		}
	}

	function handleKeyPress(event: KeyboardEvent) {
		// Handle suggestion navigation
		if (showSuggestions && filteredSuggestions.length > 0) {
			if (event.key === 'ArrowDown') {
				event.preventDefault();
				// If no suggestion is selected, select the first one
				if (selectedSuggestionIndex === -1) {
					selectedSuggestionIndex = 0;
				} else {
					// Move to next suggestion with wrap-around
					selectedSuggestionIndex = (selectedSuggestionIndex + 1) % filteredSuggestions.length;
				}
				scrollToSelectedSuggestion();
				return;
			}
			if (event.key === 'ArrowUp') {
				event.preventDefault();
				// If no suggestion is selected, select the last one (initial focus behavior)
				if (selectedSuggestionIndex === -1) {
					selectedSuggestionIndex = filteredSuggestions.length - 1;
				} else {
					// Move to previous suggestion with wrap-around
					selectedSuggestionIndex =
						selectedSuggestionIndex === 0
							? filteredSuggestions.length - 1
							: selectedSuggestionIndex - 1;
				}
				scrollToSelectedSuggestion();
				return;
			}
			if (event.key === 'Enter' && selectedSuggestionIndex >= 0) {
				event.preventDefault();
				selectSuggestion(filteredSuggestions[selectedSuggestionIndex]);
				return;
			}
			if (event.key === 'Escape') {
				event.preventDefault();
				hideSuggestionsAndReturnFocus();
				return;
			}
		}

		if (event.key === 'Enter' && !event.shiftKey) {
			event.preventDefault();
			handleSend();
		}
	}

	function handleInput() {
		// Emit typing indicator
		if (!isTyping) {
			isTyping = true;
			dispatch('typing', { isTyping: true });
		}

		// Clear existing timeout
		if (typingTimeout) {
			window.clearTimeout(typingTimeout);
		}

		// Set new timeout to stop typing indicator
		typingTimeout = window.setTimeout(() => {
			stopTyping();
		}, 1000);

		// Handle autocomplete suggestions
		checkForSuggestions();
	}

	function checkForSuggestions() {
		if (!textareaElement) return;

		const cursorPosition = textareaElement.selectionStart;
		const textBeforeCursor = messageText.substring(0, cursorPosition);

		// Find the last word before cursor
		const words = textBeforeCursor.split(/\s+/);
		const lastWord = words[words.length - 1].toLowerCase();

		if (lastWord.length >= 2) {
			// Find matching templates
			const matches = responseTemplates.filter(
				(template) =>
					template.keyword.toLowerCase().includes(lastWord) ||
					template.description.toLowerCase().includes(lastWord)
			);

			if (matches.length > 0) {
				filteredSuggestions = matches;
				currentTriggerWord = lastWord;
				triggerStartPosition = cursorPosition - lastWord.length;
				selectedSuggestionIndex = -1;
				showSuggestions = true;
			} else {
				hideSuggestions();
			}
		} else {
			hideSuggestions();
		}
	}

	function selectSuggestion(suggestion: {
		id: string;
		keyword: string;
		template: string;
		description: string;
	}) {
		if (!textareaElement) return;

		const cursorPosition = textareaElement.selectionStart;
		const beforeTrigger = messageText.substring(0, triggerStartPosition);
		const afterCursor = messageText.substring(cursorPosition);

		// Replace the trigger word with the template
		messageText = beforeTrigger + suggestion.template + afterCursor;

		// Hide suggestions first
		hideSuggestions();

		// Set cursor position after the inserted template and return focus to textarea
		setTimeout(() => {
			const newCursorPosition = beforeTrigger.length + suggestion.template.length;
			textareaElement.setSelectionRange(newCursorPosition, newCursorPosition);
			textareaElement.focus();
		}, 0);
	}

	function hideSuggestions() {
		showSuggestions = false;
		filteredSuggestions = [];
		selectedSuggestionIndex = -1;
		currentTriggerWord = '';
		triggerStartPosition = -1;
	}

	function hideSuggestionsAndReturnFocus() {
		hideSuggestions();
		// Return focus to textarea
		setTimeout(() => {
			if (textareaElement) {
				textareaElement.focus();
			}
		}, 0);
	}

	function scrollToSelectedSuggestion() {
		if (!suggestionDropdown || selectedSuggestionIndex < 0) return;

		const selectedElement = suggestionDropdown.children[selectedSuggestionIndex] as HTMLElement;
		if (selectedElement) {
			selectedElement.scrollIntoView({ block: 'nearest' });
		}
	}

	function handleClickOutside(event: MouseEvent) {
		if (
			suggestionDropdown &&
			!suggestionDropdown.contains(event.target as Node) &&
			!textareaElement.contains(event.target as Node)
		) {
			hideSuggestionsAndReturnFocus();
		}
	}

	function stopTyping() {
		if (isTyping) {
			isTyping = false;
			dispatch('typing', { isTyping: false });
		}
		if (typingTimeout) {
			window.clearTimeout(typingTimeout);
			typingTimeout = null;
		}
	}

	function handleFileClick() {
		fileInput.click();
	}

	function handleFileSelect(event: Event) {
		const input = event.target as HTMLInputElement;
		if (input.files && input.files.length > 0) {
			// เพิ่มไฟล์ใหม่เข้าไปใน array เดิม แทนการ replace
			selectedFiles = [...selectedFiles, ...Array.from(input.files)];
			showFilePreview = true;
			// Reset input เพื่อให้สามารถเลือกไฟล์เดิมซ้ำได้
			input.value = '';
		}
	}

	function removeFile(index: number) {
		selectedFiles = selectedFiles.filter((_, i) => i !== index);
		if (selectedFiles.length === 0) {
			showFilePreview = false;
		}
	}

	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}

	// Lifecycle functions
	onMount(() => {
		document.addEventListener('click', handleClickOutside);
	});

	onDestroy(() => {
		document.removeEventListener('click', handleClickOutside);
		if (typingTimeout) {
			window.clearTimeout(typingTimeout);
		}
	});
</script>

<!-- File Preview -->
{#if showFilePreview && selectedFiles.length > 0}
	<div class="border-t border-gray-200 bg-gray-50 px-4 py-3">
		<div class="mb-3 flex items-center justify-between">
			<span class="text-sm font-medium text-gray-700">ไฟล์ที่จะส่ง:</span>
			<button
				on:click={() => {
					selectedFiles = [];
					showFilePreview = false;
				}}
				class="text-sm text-red-600 hover:text-red-700"
			>
				ยกเลิกทั้งหมด
			</button>
		</div>

		<!-- File Stack -->
		<div class="relative">
			{#each selectedFiles as file, index}
				<div
					class="relative mb-2 rounded-lg border bg-white p-3 shadow-sm last:mb-0"
					style="transform: translateY({index * -2}px) translateX({index *
						2}px); z-index: {selectedFiles.length - index};"
				>
					<div class="flex items-center justify-between">
						<div class="min-w-0 flex-1">
							<div class="truncate text-sm font-medium text-gray-900">{file.name}</div>
							<div class="text-xs text-gray-500">{formatFileSize(file.size)}</div>
						</div>
						<button
							on:click={() => removeFile(index)}
							class="ml-2 rounded-full p-1 text-red-500 hover:bg-red-50 hover:text-red-700"
							title="ลบไฟล์"
						>
							<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M6 18L18 6M6 6l12 12"
								/>
							</svg>
						</button>
					</div>
				</div>
			{/each}
		</div>
	</div>
{/if}

<!-- Message Input Area -->
<div class="relative border-t border-gray-200 bg-white px-4 py-3">
	<div class="flex items-center space-x-3">
		<!-- File Upload Button -->
		<button
			on:click={handleFileClick}
			class="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full transition-colors hover:bg-gray-100"
			title="แนบไฟล์"
		>
			<svg class="h-5 w-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
				/>
			</svg>
		</button>

		<!-- Message Input -->
		<div class="flex min-w-0 flex-1">
			<!-- Suggestion Dropdown -->
			{#if showSuggestions && filteredSuggestions.length > 0}
				<div
					bind:this={suggestionDropdown}
					class="suggestion-dropdown absolute bottom-full left-0 right-0 z-50 mb-2 max-h-60 overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-lg"
				>
					{#each filteredSuggestions as suggestion, index}
						<button
							class="w-full border-b border-gray-100 px-4 py-3 text-left transition-all duration-150 last:border-b-0 hover:bg-gray-50 {index ===
							selectedSuggestionIndex
								? 'border-blue-200 bg-blue-50 ring-2 ring-blue-200 ring-opacity-50'
								: ''}"
							on:click={() => selectSuggestion(suggestion)}
							on:mouseenter={() => (selectedSuggestionIndex = index)}
						>
							<div class="flex items-start justify-between">
								<div class="min-w-0 flex-1">
									<div class="truncate text-sm font-medium text-gray-900">
										{suggestion.keyword}
									</div>
									<div class="mt-1 text-xs text-gray-500">
										{suggestion.description}
									</div>
								</div>
								<div class="ml-2 flex-shrink-0">
									<span
										class="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800"
									>
										Template
									</span>
								</div>
							</div>
							<div class="mt-2 line-clamp-2 text-xs text-gray-600">
								{suggestion.template}
							</div>
						</button>
					{/each}
				</div>
			{/if}

			<textarea
				bind:this={textareaElement}
				bind:value={messageText}
				on:keypress={handleKeyPress}
				on:input={handleInput}
				placeholder={t('type_message')}
				rows="1"
				class="w-full resize-none rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
				style="min-height: 40px; max-height: 120px;"
			/>
		</div>

		<!-- Send Button -->
		<button
			on:click={handleSend}
			disabled={!messageText.trim() && selectedFiles.length === 0}
			class="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full transition-colors {messageText.trim() ||
			selectedFiles.length > 0
				? 'bg-blue-500 text-white shadow-sm hover:bg-blue-600'
				: 'cursor-not-allowed bg-gray-100 text-gray-400'}"
			title="ส่งข้อความ"
		>
			<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
				/>
			</svg>
		</button>
	</div>
</div>

<!-- Hidden File Input -->
<input
	bind:this={fileInput}
	type="file"
	multiple
	on:change={handleFileSelect}
	class="hidden"
	accept="*/*"
/>

<style>
	textarea {
		overflow-y: auto;
		line-height: 1.5;
	}

	textarea:focus {
		resize: none;
	}

	/* Custom scrollbar for file preview */
	.relative {
		min-height: 60px;
	}

	/* Line clamp utility for suggestion templates */
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	/* Suggestion dropdown animations */
	.suggestion-dropdown {
		animation: slideUp 0.2s ease-out;
	}

	@keyframes slideUp {
		from {
			opacity: 0;
			transform: translateY(10px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* Custom scrollbar for suggestions */
	.suggestion-dropdown::-webkit-scrollbar {
		width: 6px;
	}

	.suggestion-dropdown::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 3px;
	}

	.suggestion-dropdown::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 3px;
	}

	.suggestion-dropdown::-webkit-scrollbar-thumb:hover {
		background: #a8a8a8;
	}

	/* Enhanced visual feedback for keyboard navigation */
	.suggestion-dropdown button:focus {
		outline: none;
	}

	/* Smooth transitions for selection changes */
	.suggestion-dropdown button {
		transition: all 0.15s ease-in-out;
	}

	/* Enhanced selected state styling */
	.suggestion-dropdown button.selected {
		background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
		border-color: #3b82f6;
		box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
		transform: translateY(-1px);
	}
</style>
